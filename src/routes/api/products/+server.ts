import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ locals }) => {
	// Validate permission using locals.can
	if (!(await locals.can('products:read'))) {
		return json([]);
	}

	// Get products from database with brand and category names
	const products = await locals.db
		.selectFrom('products')
		.leftJoin('brands', 'products.brand_code', 'brands.code')
		.leftJoin('categories', 'products.category_code', 'categories.code')
		.select([
			'products.code',
			'products.name',
			'products.description',
			'products.brand_code',
			'products.category_code',
			'products.price',
			'products.sku',
			'products.images',
			'products.is_active',
			'products.created_at',
			'products.updated_at',
			'brands.name as brand_name',
			'categories.name as category_name'
		])
		.orderBy('products.name', 'asc')
		.execute();

	return json(products);
};

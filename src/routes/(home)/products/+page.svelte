<!-- src/routes/products/+page.svelte -->
<script lang="ts">
	import { invalidate } from '$app/navigation';
	import Message from '$lib/components/Message.svelte';
	import PageTitle from '$lib/components/PageTitle.svelte';
	import Table from '$lib/components/Table.svelte';
	import { showToast } from '$lib/stores/Toast';
	import { Package, Plus, Edit, Trash2, Search } from 'lucide-svelte';
	import { formatDate } from '$lib/utils/formatDate';
	import type { Brands, Categories } from '$lib/types';
	import { can } from '$lib/stores/permissions';

	// Product type with joined data
	interface ProductWithJoins {
		code: string;
		name: string;
		description: string | null;
		brand_code: string;
		category_code: string;
		price: number;
		sku: string | null;
		image_url: string | null;
		is_active: boolean;
		created_at: string;
		updated_at: string;
		brand_name: string | null;
		category_name: string | null;
		user_name: string | null;
	}

	// Estados y referencias
	let modal: HTMLDialogElement | null = $state(null);
	let confirmModal: HTMLDialogElement | null = $state(null);
	let isEditing = $state(false);
	let message = $state('');
	let selectedProduct = $state<ProductWithJoins | null>(null);

	// Search and filter states
	let searchTerm = $state('');
	let selectedBrand = $state('');
	let selectedCategory = $state('');
	let showInactive = $state(false);
	let sortField = $state<string>('name');
	let sortDirection = $state<'asc' | 'desc'>('asc');

	const { data } = $props<{
		data: {
			products: ProductWithJoins[];
			brands: Brands[];
			categories: Categories[];
		};
	}>();

	// Permissions
	let canCreate = $derived(can('products:create'));
	let canUpdate = $derived(can('products:update'));
	let canDelete = $derived(can('products:delete'));

	// Filtered and sorted products
	let filteredProducts = $derived(() => {
		let filtered = data.products.filter((product: ProductWithJoins) => {
			const matchesSearch =
				!searchTerm ||
				product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
				product.sku?.toLowerCase().includes(searchTerm.toLowerCase()) ||
				product.brand_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
				product.category_name?.toLowerCase().includes(searchTerm.toLowerCase());

			const matchesBrand = !selectedBrand || product.brand_code === selectedBrand;
			const matchesCategory = !selectedCategory || product.category_code === selectedCategory;
			const matchesActive = showInactive || product.is_active;

			return matchesSearch && matchesBrand && matchesCategory && matchesActive;
		});

		// Simple sorting
		if (sortField && sortDirection) {
			filtered.sort((a: ProductWithJoins, b: ProductWithJoins) => {
				let aVal = a[sortField as keyof ProductWithJoins];
				let bVal = b[sortField as keyof ProductWithJoins];

				if (sortField === 'price') {
					aVal = Number(aVal) || 0;
					bVal = Number(bVal) || 0;
				} else {
					aVal = String(aVal).toLowerCase();
					bVal = String(bVal).toLowerCase();
				}

				if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1;
				if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1;
				return 0;
			});
		}

		return filtered;
	});

	// Table columns - simplified
	const columns = [
		{
			label: 'Producto',
			class: 'font-medium',
			render: productCell
		},
		{
			key: 'brand_name' as keyof ProductWithJoins,
			label: 'Marca',
			class: 'text-sm'
		},
		{
			key: 'category_name' as keyof ProductWithJoins,
			label: 'Categoría',
			class: 'text-sm'
		},
		{
			label: 'Precio',
			class: 'font-mono font-medium',
			render: priceCell
		},
		{
			label: 'Estado',
			render: statusCell
		},
		{
			label: 'Creado',
			class: 'text-xs opacity-60',
			render: dateCell
		},
		{
			label: 'Acciones',
			class: 'text-right',
			render: actionsCell
		}
	];

	// Functions
	function openCreateModal() {
		if (!canCreate) return;
		isEditing = false;
		selectedProduct = null;
		message = '';
		modal?.showModal();
	}

	function openEditModal(productCode: string) {
		if (!canUpdate) return;
		const product = data.products.find((p: ProductWithJoins) => p.code === productCode);
		if (!product) return;

		isEditing = true;
		selectedProduct = product;
		message = '';
		modal?.showModal();

		// Pre-fill form
		setTimeout(() => {
			const form = modal?.querySelector('form');
			if (form) {
				(form.querySelector('#name') as HTMLInputElement).value = product.name;
				(form.querySelector('#description') as HTMLTextAreaElement).value =
					product.description || '';
				(form.querySelector('#brand_code') as HTMLSelectElement).value = product.brand_code;
				(form.querySelector('#category_code') as HTMLSelectElement).value = product.category_code;
				(form.querySelector('#price') as HTMLInputElement).value = product.price;
				(form.querySelector('#sku') as HTMLInputElement).value = product.sku || '';
				(form.querySelector('#image_url') as HTMLInputElement).value = product.image_url || '';
				(form.querySelector('#is_active') as HTMLInputElement).checked = product.is_active;
			}
		}, 50);
	}

	function confirmDelete(productCode: string) {
		if (!canDelete) return;
		const product = data.products.find((p: ProductWithJoins) => p.code === productCode);
		if (!product) return;

		selectedProduct = product;
		confirmModal?.showModal();
	}

	function clearFilters() {
		searchTerm = '';
		selectedBrand = '';
		selectedCategory = '';
		showInactive = false;
		sortField = 'name';
		sortDirection = 'asc';
	}

	function toggleSort(field: string) {
		if (sortField === field) {
			sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
		} else {
			sortField = field;
			sortDirection = 'asc';
		}
	}

	async function handleSubmit(event: SubmitEvent) {
		event.preventDefault();
		const formData = new FormData(event.target as HTMLFormElement);

		if (isEditing && selectedProduct) {
			formData.append('code', selectedProduct.code);
		}

		const action = isEditing ? '?/update' : '?/create';

		try {
			const response = await fetch(action, {
				method: 'POST',
				body: formData
			});

			const result = await response.json();

			if (result.type === 'success') {
				showToast('Producto guardado correctamente', 'success');
				modal?.close();
				await invalidate('products:load');
			} else {
				message = result.data?.error || 'Error al guardar producto';
			}
		} catch {
			message = 'Error de conexión';
		}
	}

	async function handleDelete() {
		if (!selectedProduct) return;

		try {
			const formData = new FormData();
			formData.append('code', selectedProduct.code);

			const response = await fetch('?/delete', {
				method: 'POST',
				body: formData
			});

			const result = await response.json();

			if (result.type === 'success') {
				showToast('Producto eliminado correctamente', 'success');
				confirmModal?.close();
				await invalidate('products:load');
			} else {
				showToast(result.data?.error || 'Error al eliminar producto', 'danger');
			}
		} catch {
			showToast('Error de conexión', 'danger');
		}
	}
</script>

<PageTitle title="Productos" description="Gestiona el catálogo de productos de tu tienda online.">
	<button class="btn btn-primary" onclick={openCreateModal} disabled={!canCreate}>
		<Plus class="w-4 h-4" />
		Nuevo Producto
	</button>
</PageTitle>

<!-- Search and Filters -->
<div class="bg-base-100 rounded-lg border border-base-300 p-4 mb-6">
	<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
		<!-- Search -->
		<div class="relative">
			<Search class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 opacity-50" />
			<input
				type="text"
				placeholder="Buscar productos..."
				class="input input-bordered w-full pl-10"
				bind:value={searchTerm}
			/>
		</div>

		<!-- Brand Filter -->
		<select class="select select-bordered" bind:value={selectedBrand}>
			<option value="">Todas las marcas</option>
			{#each data.brands as brand (brand.code)}
				<option value={brand.code}>{brand.name}</option>
			{/each}
		</select>

		<!-- Category Filter -->
		<select class="select select-bordered" bind:value={selectedCategory}>
			<option value="">Todas las categorías</option>
			{#each data.categories as category (category.code)}
				<option value={category.code}>{category.name}</option>
			{/each}
		</select>

		<!-- Show Inactive -->
		<label class="flex items-center gap-2 cursor-pointer">
			<input type="checkbox" class="checkbox checkbox-sm" bind:checked={showInactive} />
			<span class="text-sm">Mostrar inactivos</span>
		</label>

		<!-- Sort -->
		<div class="flex gap-1">
			<button
				class="btn btn-outline btn-sm"
				onclick={() => toggleSort('name')}
				class:btn-active={sortField === 'name'}
			>
				Nombre {#if sortField === 'name'}{sortDirection === 'asc' ? '↑' : '↓'}{/if}
			</button>
			<button
				class="btn btn-outline btn-sm"
				onclick={() => toggleSort('price')}
				class:btn-active={sortField === 'price'}
			>
				Precio {#if sortField === 'price'}{sortDirection === 'asc' ? '↑' : '↓'}{/if}
			</button>
		</div>

		<!-- Clear Filters -->
		<button class="btn btn-ghost btn-sm" onclick={clearFilters}> Limpiar filtros </button>
	</div>
</div>

<!-- Results Summary -->
<div class="flex items-center justify-between mb-4">
	<div class="text-sm opacity-60">
		Mostrando {filteredProducts.length} de {data.products.length} productos
	</div>
</div>

<!-- Products Table -->
<div class="bg-base-100 rounded-lg border border-base-300 overflow-hidden">
	<Table
		{columns}
		rows={filteredProducts()}
		emptyMessage="No se encontraron productos"
		className="border-0"
	/>
</div>

<!-- Modal para crear/editar -->
<dialog bind:this={modal} class="modal">
	<div class="modal-box max-w-2xl">
		<h3 class="text-lg font-bold mb-4">
			{isEditing ? 'Editar Producto' : 'Nuevo Producto'}
		</h3>

		{#if message}
			<div class="mb-4">
				<Message description={message} type="error" />
			</div>
		{/if}

		<form onsubmit={handleSubmit} class="space-y-4">
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				<!-- Nombre -->
				<div class="form-control">
					<label class="label" for="name">
						<span class="label-text">Nombre *</span>
					</label>
					<input
						id="name"
						name="name"
						type="text"
						class="input input-bordered"
						placeholder="Nombre del producto"
						required
					/>
				</div>

				<!-- SKU -->
				<div class="form-control">
					<label class="label" for="sku">
						<span class="label-text">SKU</span>
					</label>
					<input
						id="sku"
						name="sku"
						type="text"
						class="input input-bordered"
						placeholder="Código del producto"
					/>
				</div>
			</div>

			<!-- Descripción -->
			<div class="form-control">
				<label class="label" for="description">
					<span class="label-text">Descripción</span>
				</label>
				<textarea
					id="description"
					name="description"
					class="textarea textarea-bordered"
					placeholder="Descripción del producto"
					rows="3"
				></textarea>
			</div>

			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				<!-- Marca -->
				<div class="form-control">
					<label class="label" for="brand_code">
						<span class="label-text">Marca *</span>
					</label>
					<select id="brand_code" name="brand_code" class="select select-bordered" required>
						<option value="">Seleccionar marca</option>
						{#each data.brands as brand (brand.code)}
							<option value={brand.code}>{brand.name}</option>
						{/each}
					</select>
				</div>

				<!-- Categoría -->
				<div class="form-control">
					<label class="label" for="category_code">
						<span class="label-text">Categoría *</span>
					</label>
					<select id="category_code" name="category_code" class="select select-bordered" required>
						<option value="">Seleccionar categoría</option>
						{#each data.categories as category (category.code)}
							<option value={category.code}>{category.name}</option>
						{/each}
					</select>
				</div>
			</div>

			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				<!-- Precio -->
				<div class="form-control">
					<label class="label" for="price">
						<span class="label-text">Precio *</span>
					</label>
					<input
						id="price"
						name="price"
						type="number"
						step="0.01"
						min="0"
						class="input input-bordered"
						placeholder="0.00"
						required
					/>
				</div>

				<!-- Estado (solo en edición) -->
				{#if isEditing}
					<div class="form-control">
						<label class="label" for="is_active">
							<span class="label-text">Estado</span>
						</label>
						<label class="cursor-pointer label justify-start gap-3">
							<input id="is_active" name="is_active" type="checkbox" class="checkbox" />
							<span class="label-text">Producto activo</span>
						</label>
					</div>
				{/if}
			</div>

			<!-- URL de imagen -->
			<div class="form-control">
				<label class="label" for="image_url">
					<span class="label-text">URL de imagen</span>
				</label>
				<input
					id="image_url"
					name="image_url"
					type="url"
					class="input input-bordered"
					placeholder="https://ejemplo.com/imagen.jpg"
				/>
			</div>

			<div class="modal-action">
				<button type="button" class="btn" onclick={() => modal?.close()}>Cancelar</button>
				<button type="submit" class="btn btn-primary">
					{isEditing ? 'Actualizar' : 'Crear'}
				</button>
			</div>
		</form>
	</div>
</dialog>

<!-- Modal de confirmación para eliminar -->
<dialog bind:this={confirmModal} class="modal">
	<div class="modal-box">
		<h3 class="text-lg font-bold">Confirmar eliminación</h3>
		<p class="py-4">
			¿Estás seguro de que deseas eliminar el producto
			<strong>{selectedProduct?.name}</strong>? Esta acción no se puede deshacer.
		</p>
		<div class="modal-action">
			<button class="btn" onclick={() => confirmModal?.close()}>Cancelar</button>
			<button class="btn btn-error" onclick={handleDelete}>Eliminar</button>
		</div>
	</div>
</dialog>

{#snippet productCell(product: ProductWithJoins)}
	<div class="flex items-center gap-3">
		{#if product.image_url}
			<img
				src={product.image_url}
				alt={product.name}
				class="w-10 h-10 rounded-lg object-cover bg-base-200"
			/>
		{:else}
			<div class="w-10 h-10 rounded-lg bg-base-200 flex items-center justify-center">
				<Package class="w-5 h-5 opacity-50" />
			</div>
		{/if}
		<div>
			<div class="font-medium">{product.name}</div>
			{#if product.sku}
				<div class="text-xs opacity-60">SKU: {product.sku}</div>
			{/if}
		</div>
	</div>
{/snippet}

{#snippet priceCell(product: ProductWithJoins)}
	<span class="font-mono font-medium">${Number(product.price).toFixed(2)}</span>
{/snippet}

{#snippet statusCell(product: ProductWithJoins)}
	<div class="badge {product.is_active ? 'badge-success' : 'badge-error'} badge-sm">
		{product.is_active ? 'Activo' : 'Inactivo'}
	</div>
{/snippet}

{#snippet dateCell(product: ProductWithJoins)}
	<span class="text-xs opacity-60">{formatDate(product.created_at)}</span>
{/snippet}

{#snippet actionsCell(product: ProductWithJoins)}
	<div class="flex gap-1 justify-end">
		{#if canUpdate}
			<button class="btn btn-ghost btn-xs" onclick={() => openEditModal(product.code)}>
				<Edit class="w-3 h-3" />
			</button>
		{/if}
		{#if canDelete}
			<button class="btn btn-ghost btn-xs text-error" onclick={() => confirmDelete(product.code)}>
				<Trash2 class="w-3 h-3" />
			</button>
		{/if}
	</div>
{/snippet}
